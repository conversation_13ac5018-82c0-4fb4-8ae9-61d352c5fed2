<!--
 * @Description: 问卷管理
 * @Autor: Fhz
 * @Date: 2025-02-11 15:30:59
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-01 14:26:21
-->
<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <Spin :spinning="loading" size="large">
          <div class="fw-title">问卷管理</div>
          <div class="fw-tip">
            <Mtcn-Input v-model="keyWord" placeholder="请输入问卷名称关键词搜索" />
            <a-button @click="reload" type="primary">搜索</a-button>
          </div>
          <div class="fw-tip">
            <a-button @click="handleAdd({ id: '', operationType: 0 })" type="primary" preIcon="icon-ym icon-ym-btn-add">创建问卷</a-button>
          </div>
          <div class="portalMain">
            <template v-for="(item, index) in batchList" :key="index">
              <div :class="['card', item.sfsy == 1 ? 'cardSel' : '']">
                <div class="cardTop">
                  <p class="cardTitle"> {{ item.wjmc }}</p>
                </div>
                <div class="cardOpts">
                  <p class="link" @click="handleAdd({ id: item.id, operationType: 1 })">编辑</p>
                  <div class="split"></div>
                  <a-switch v-model:checked="item.sfsy" checked-children="启用" un-checked-children="停用" @change="handleEnable(item)"></a-switch>
                  <div class="split"></div>
                  <a-dropdown>
                    <a class="flex items-center" @click.prevent>
                      更多
                      <DownOutlined class="ml-5px" />
                    </a>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item @click="handleCopy(item)"> 复制 </a-menu-item>
                        <a-menu-item @click="openQuestionAddressModal(true, item)"> 调用地址 </a-menu-item>
                        <a-menu-item @click="handleDelete(item)" style="color: #ed6f6f"> 删除 </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </div>
              </div>
            </template>
          </div>
        </Spin>
      </div>
      <!-- 创建问卷模板、题目 -->
      <StepPopup @register="registerForm" @reload=""></StepPopup>
      <!-- 调用地址 -->
      <QuestionAddressModal @register="registerQuestionAddressModal" />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { usePopup } from '@/components/Popup';
  import { useModal } from '@/components/Modal';
  import { Spin, Empty } from 'ant-design-vue';
  import { DownOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import StepPopup from './components/StepPopup.vue';
  import QuestionAddressModal from './components/QuestionAddressModal.vue';
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const api = useBaseApi('/api/knsDcwj');
  const { createMessage, createConfirm } = useMessage();
  const [registerForm, { openPopup: openFormPopup }] = usePopup();
  const [registerQuestionAddressModal, { openModal: openQuestionAddressModal }] = useModal();
  const batchList = ref([]);
  const loading = ref(false);
  const keyWord = ref('');
  function handleAdd(record) {
    openFormPopup(true, record);
  }

  async function reload() {
    loading.value = true;
    const {
      data: { list },
    } = await api.getList({ params: { pageSize: 9999, currentPage: 1, keyWord: keyWord.value } });
    batchList.value = list;
    loading.value = false;
  }
  async function handleDelete(record) {
    const { msg } = await api.remove({ params: { id: record.id } });
    if (success) {
      createMessage.success(msg);
      reload();
    }
  }
  async function handleEnable(record) {
    const { msg } = await api.request('put', `/api/knsDcwj/editByZt/${record.id}`, { params: { sfsy: record.sfsy ? 1 : 0 }, isFullPath: true });
    createMessage.success(msg);
    reload();
  }

  async function handleCopy(record) {
    createConfirm({
      title: '确认复制问卷',
      content: `是否确定要复制该问卷？`,
      onOk: () => handleCopySure(record),
    });
  }

  async function handleCopySure(record) {
    const { msg } = await api.request('get', `/copy/${record.id}`, { params: { id: record.id } });
    createMessage.success(msg);
    reload();
  }

  onMounted(() => {
    reload();
  });
</script>
<style lang="less" scoped>
  .mtcn-content-wrapper-content {
    background: #fff;
    position: relative;

    overflow: scroll !important;
    .empty {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .fw-tip {
    margin: 0 20px 20px 20px;
    color: #666666;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 10px;
    .ant-input {
      width: 300px;
    }
  }
  .fw-title {
    margin: 20px;
    padding-left: 16px;
    font-weight: bold;
    position: relative;
    font-size: 18px;
  }
  .fw-title::before {
    content: '';
    width: 4px;
    height: 15px;
    border-radius: 4px;
    background-color: @primary-color;
    display: inline-block;
    position: absolute;
    top: 50%;
    left: 6px;
    transform: translate(-50%, -50%);
    margin-right: 8px;
  }
  .portalMain {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));

    margin: 20px;
    grid-gap: 10px;
    .card {
      border: 1px solid #edeff2;
      border-radius: 4px;
      position: relative;
      overflow: hidden;
      .cardTop {
        padding: 16px;
        .cardTitle {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          word-break: break-all;
          font-size: 16px;
          // font-weight: 700;
          margin-bottom: 16px;
        }
        .cardTemplate {
          margin-top: 4px;
          color: #86909c;
          font-size: 14px;
          line-height: 22px;
          span {
            color: #4e5969;
          }
        }
      }
      .cardOpts {
        padding: 11px 12px;
        border-top: 1px solid #edeff2;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-around;

        .link {
          border-color: transparent;
          color: @primary-color;
          background: 0 0;
          display: inline-block;
          line-height: 1;
          white-space: nowrap;
          cursor: pointer;
        }
        .split {
          width: 1px;
          height: 12px;
          margin: 0 12px;
          background-color: #dfe2e8;
          flex-shrink: 0;
        }
      }
    }
    .cardSel::after {
      content: '已启用';
      top: 10px;
      right: -20px;
      width: 80px;
      height: 20px;
      line-height: 20px;
      position: absolute;
      transform: rotate(45deg);
      font-size: 12px;
      text-align: center;
      color: #fff;
      background: #00b42a;
    }
  }
</style>

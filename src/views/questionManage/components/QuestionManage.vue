<!--
 * @Description: 问卷题目
 * @Autor: Fhz
 * @Date: 2025-04-21 16:38:40
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-01 14:04:29
-->
<template>
  <a-row class="mb-100px">
    <a-col :span="14" :offset="5">
      <Spin :spinning="spinning">
        <ScrollContainer>
          <div class="serviceItemFieldsConfig">
            <a-button type="primary" @click="openAddFormModal(true, { id: null, wjdm: props.shareInfo.wjdm })" preIcon="icon-ym icon-ym-btn-add">
              添加问卷题目
            </a-button>
            <div class="table-tbody questionList">
              <div v-for="item in questionList" :key="item.id" class="fieldItem">
                <div class="itemLeft">
                  <div class="iconBox">
                    <i class="drag-handler icon-ym icon-ym-darg" title="点击拖动" />
                  </div>
                  <a-tag color="blue">{{ { '1': '单', '2': '多', '3': '简' }[item.tmlx] }}</a-tag>
                  <span>{{ item.tmmc }}</span>
                  <span class="required">*</span>
                </div>
                <div class="itemRight">
                  <a @click="openAddFormModal(true, { id: item.id })">编辑</a>
                  <a v-if="item.tmlx != 3" @click="openOptFormModal(true, { ...item, wjdm: props.shareInfo.wjdm })">添加选项</a>
                  <a @click="handleDelete(item)" style="color: #ed6f6f">删除</a>
                </div>
              </div>
            </div>
          </div>
        </ScrollContainer>
      </Spin>
      <!-- 添加题目 -->
      <AddQuestionForm @register="registerAddForm" @reload="getDetail"></AddQuestionForm>
      <!-- 添加选项 -->
      <AddQuesOptionForm @register="registerOptForm" @reload="getDetail" />
    </a-col>
  </a-row>
</template>
<script lang="ts" setup>
  import { reactive, toRefs, ref, computed, onMounted, watch, unref, nextTick } from 'vue';
  import { ScrollContainer } from '@/components/Container';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useModal } from '@/components/Modal';
  import draggable from 'vuedraggable';
  import { Spin } from 'ant-design-vue';
  import { DownOutlined, PlusOutlined, SwapOutlined, FileOutlined } from '@ant-design/icons-vue';
  import {
    getQueryAllList,
    editServiceMatters,
    delAloneField,
    fetchAloneList,
    editAloneField,
    handleEnable,
    handleOrder,
  } from '@/api/serviceMatters/ItemFieldsConfig';
  import AddQuestionForm from './AddQuestionForm.vue';
  import AddQuesOptionForm from './AddQuesOptionForm.vue';
  import Sortablejs from 'sortablejs';
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const emit = defineEmits(['register', 'reload']);
  const props = defineProps(['stepIds', 'stepItems', 'shareInfo']);
  const api = useBaseApi('/api/knsDcwjTm');
  const { createMessage, createConfirm } = useMessage();
  const spinning = ref<Boolean>(false);
  const questionList = ref([]);
  const state = reactive({
    isDrag: false,
  });
  // 添加题目
  const [registerAddForm, { openModal: openAddFormModal }] = useModal();
  // 添加选项
  const [registerOptForm, { openModal: openOptFormModal }] = useModal();
  function initSort() {
    if (questionList.value.length == 0) return;
    const searchTable: any = document.querySelector(`.table-tbody`);
    Sortablejs.create(searchTable, {
      handle: '.drag-handler',
      animation: 150,
      easing: 'cubic-bezier(1, 0, 0, 1)',
      onStart: () => {},
      onEnd: async ({ newIndex, oldIndex }: any) => {
        try {
          spinning.value = true;
          const currRow = questionList.value.splice(oldIndex, 1)[0];
          questionList.value.splice(newIndex, 0, currRow);
          const sortList = questionList.value.map((item, index) => ({
            id: item.id,
            msmfOrder: index,
          }));
          const res = await handleOrder(sortList);
          createMessage.success(res.msg);
        } finally {
          spinning.value = false;
        }
      },
    });
  }
  function handleDelete(item) {
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '确认要删除吗？',
      onOk: () => {
        api.remove({ params: { id: item.id } }).then(() => {
          createMessage.success('删除成功');
          getDetail();
        });
      },
    });
  }
  function save() {
    const generatedId = 'new-generated-id';
    return {
      success: true,
      id: generatedId,
      errorMessage: '',
    };
  }
  async function getDetail() {
    const { data } = await api.getListAll({ wjdm: props.shareInfo.wjdm });
    questionList.value = data;
    initSort();
  }
  defineExpose({
    save,
    getDetail,
  });
  onMounted(() => {
    nextTick(() => {
      initSort();
    });
  });
</script>

<style lang="less" scoped>
  .serviceItemFieldsConfig {
    width: 100%;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin: 0 auto;
  }

  .questionList {
    list-style: none;
    padding: 0;
    cursor: pointer;
    padding-top: 10px;
    .fieldItem {
      height: 40px;
      border: 1px solid #bbbec4;
      border-radius: 5px;
      padding: 0 10px;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &.notUse {
        opacity: 0.6;
        .itemRight a {
          cursor: default;
        }
      }
      .itemLeft {
        display: flex;
        align-items: center;
        .iconBox {
          width: 25px;
          cursor: pointer;
          :deep(.anticon) {
            cursor: move;
          }
        }
        .required {
          display: inline-block;
          margin-left: 10px;
          color: red;
          font-size: 16px;
          padding-top: 5px;
        }
        :deep(.ant-tag) {
          margin: 0 4px 0 10px;
        }
        .tip {
          font-size: 12px;
          color: #8c8c8c;
          opacity: 0.7;
        }
      }
      .itemRight {
        font-size: 13px;
        display: flex;
        align-items: center;
        a {
          font-size: 13px;
          padding: 0 8px;
          height: 13px;
          line-height: 13px;
          color: #2d8cf0;
          border-left: 1px solid #d9d9d9;
          user-select: none;
          &:first-child {
            border: none;
          }
        }
        .disable-btn {
          color: rgba(0, 0, 0, 0.25);
          cursor: not-allowed;
        }
        :deep(.ant-switch) {
          margin-left: 15px;
        }
      }
    }
  }
</style>

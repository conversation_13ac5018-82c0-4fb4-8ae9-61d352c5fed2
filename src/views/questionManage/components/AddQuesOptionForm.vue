<!--
 * @Description: 添加选项
 * @Autor: panmy
 * @Date: 2024-11-04 15:42:00
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-01 14:06:02
-->
<template>
  <div class="common-container">
    <BasicModal v-bind="$attrs" class="transfer-modal member-modal" @register="registerModal" title="添加选项" :showOkBtn="false" :width="900">
      <template #footer>
        <div class="flex justify-between">
          <a-button type="primary" preIcon="icon-ym icon-ym-btn-add" @click="handleAdd()">新增一行</a-button>
          <div>
            <a-button @click="handleCancel()">取消</a-button>
            <a-button type="primary" @click="handleSave">保存</a-button>
          </div>
        </div>
      </template>

      <div class="spin-box">
        <BasicTable v-if="!isDelete" @register="registerTable" class="mtcn-sub-table">
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'sortCode'">
              <i class="drag-handler icon-ym icon-ym-darg" title="点击拖动" />
            </template>
            <template v-if="column.key === 'xxmc'">
              <a-input v-model:value="record.xxmc" placeholder="请输入选项名称" allowClear />
            </template>
            <template v-if="column.key === 'xxdm'">
              <a-input v-model:value="record.xxdm" placeholder="请输入选项值" allowClear />
            </template>
            <template v-if="column.key === 'fz'">
              <a-input-number v-model:value="record.fz" v-if="state.syfz" placeholder="请输入分值" />
              <span v-else>--</span>
            </template>
            <template v-if="column.key === 'xxlx'">
              <MtcnSelect v-model:value="record.xxlx" placeholder="请选择" :allowClear="false" :options="inputOptions" />
            </template>
            <template v-if="column.key === 'options'">
              <a-button class="action-btn" type="link" color="error" @click="handleDel(record)" size="small">删除</a-button>
            </template>
          </template>
        </BasicTable>
      </div>
    </BasicModal>
  </div>
</template>
<script lang="ts" setup>
  import { ref, watch, computed, nextTick, onMounted, reactive, readonly, toRaw } from 'vue';
  import { Form, Spin } from 'ant-design-vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import Sortablejs from 'sortablejs';
  import { BasicTable, useTable, BasicColumn } from '@/components/Table';
  import { buildBitUUID } from '@/utils/uuid';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useBaseStore } from '@/store/modules/base';
  import { getQueryAllList, addMattersField, delAloneField, handleOrder } from '@/api/serviceMatters/ItemFieldsConfig';
  import { getDictionaryTypeSelector } from '@/api/systemData/dictionary';
  import { getDataModelList } from '@/api/systemData/dataModel';
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const api = useBaseApi('/api/knsDcwjXx');
  const baseStore = useBaseStore();
  defineOptions({ inheritAttrs: false });

  const emit = defineEmits(['register', 'reload']);
  const { createMessage, createConfirm } = useMessage();
  const quesOpt = ref<any[]>([]);
  const inputOptions = [
    { id: 1, fullName: '普通选项' },
    { id: 2, fullName: '其他答案' },
  ];

  const isDelete = ref(false);
  const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(init);
  const state = ref({});
  function init(data) {
    state.value = { ...data };
    reload();
    nextTick(() => {
      initSort();
    });
  }
  const [registerTable, { getForm, reload, setTableData, getDataSource, insertTableDataRecord, setLoading }] = useTable({
    api: params => api.getList({ params }),
    beforeFetch: params => {
      params.id = state.value.id;
      params.wjdm = state.value.wjdm;
      params.tmdm = state.value.tmdm;
    },
    maxHeight: 400,
    minHeight: 300,
    columns: [
      {
        title: '',
        dataIndex: 'sortCode',
        width: 50,
        align: 'center',
      },
      {
        title: '选项名称',
        dataIndex: 'xxmc',
        width: 120,
        align: 'center',
      },
      {
        title: '选项值',
        dataIndex: 'xxdm',
        width: 120,
        align: 'center',
      },
      {
        title: '分值',
        dataIndex: 'fz',
        width: 80,
        align: 'center',
      },
      {
        title: '选项类型',
        dataIndex: 'xxlx',
        width: 120,
        align: 'center',
      },
      {
        title: '操作',
        dataIndex: 'options',
        width: 80,
        align: 'center',
      },
    ],
    immediate: false,
    useSearchForm: false,
    showTableSetting: false,
    isCanResizeParent: true,
    showIndexColumn: false,
    // resizeHeightOffset: -74,
    // rowSelection: { type: 'checkbox' },
    pagination: false,
    rowKey: 'id',
  });

  function handleAdd() {
    const id = buildBitUUID();
    const tabData = getDataSource();
    insertTableDataRecord({
      id,
      isNew: true,
      xxmc: '', // 选项名称
      xxdm: '', // 选项值
      fz: true, // 是否使用分值
      xxlx: 1, // 选项类型
      sortCode: tabData.length + 1,
    });
    initSort();
  }
  function handleDel(record: any) {
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '确认删除吗？',
      onOk: async () => {
        isDelete.value = true;
        if (record.isNew) {
          const tabData = getDataSource();
          tabData = tabData.filter(o => o.id != record.id);
          setTableData(tabData);
          isDelete.value = false;
          nextTick(() => {
            initSort();
          });
        } else {
          await api
            .remove({ params: { id: record.id } })
            .then(res => {
              createMessage.success(res.msg);
              isDelete.value = false;
              reload();
            })
            .catch(() => {
              isDelete.value = false;
            });
        }
      },
    });
  }
  function initSort() {
    const tabData = getDataSource();
    if (tabData.length == 0) return;
    const searchTable = document.querySelector('.mtcn-content-wrapper-content .ant-table-tbody');
    Sortablejs.create(searchTable, {
      handle: '.drag-handler',
      animation: 150,
      easing: 'cubic-bezier(1, 0, 0, 1)',
      onStart: evt => {},
      onEnd: async ({ newIndex, oldIndex }) => {
        try {
          setLoading(true);
          if (checkField()) {
            const list = listDataSource.value;
            const currRow = list.splice(oldIndex - 1, 1)[0];
            list.splice(newIndex - 1, 0, currRow);
            listDataSource.value = list;
          }
        } catch (error) {
          createMessage.warning(error.message);
        } finally {
          setLoading(false);
        }
      },
    });
  }

  function handleCancel() {
    closeModal();
    emit('reload');
  }
  function handleSave() {
    const tabData = getDataSource();
    if (tabData.length === 0) {
      createMessage.warning('请添加选项');
      return;
    }
    tabData.map(item => {
      if (item.isNew) {
        item.wjdm = state.value.wjdm;
        item.tmdm = state.value.tmdm;
        item.id = '';
      }
    });
    api.request('post', `/api/knsDcwjXx/batchSave/${state.value.tmdm}`, { params: tabData, isFullPath: true }).then(res => {
      createMessage.success('保存成功');
      closeModal();
      emit('reload');
    });
  }
</script>
<style lang="less" scoped>
  :deep(.ant-select-multiple .ant-select-selection-item-content),
  :deep(.ant-input-affix-wrapper-disabled .ant-input[disabled]),
  :deep(.ant-select-single .ant-select-selector .ant-select-selection-item),
  :deep(.ant-checkbox + span) {
    color: #333333;
  }
  :deep(.ant-table-body) {
    height: 100% !important;
    max-height: 100% !important;
  }
  .spin-box {
    height: 100%;
    :deep(.ant-spin-nested-loading) {
      height: 100% !important;
      position: unset;
    }
  }
</style>
